2025-06-30 16:30:49 | INFO     | __main__:info:253 - 文件信息:
2025-06-30 16:30:49 | INFO     | __main__:info:254 -   文件名: example_phones.csv
2025-06-30 16:30:49 | INFO     | __main__:info:255 -   文件大小: 340 字节
2025-06-30 16:30:49 | INFO     | __main__:info:256 -   文件格式: .csv
2025-06-30 16:30:49 | INFO     | __main__:info:257 -   是否支持: 是
2025-06-30 16:30:49 | INFO     | __main__:info:260 -   列名: ['电话', '姓名', '备注']
2025-06-30 16:30:49 | INFO     | src.phone_reader:_read_csv:64 - 使用编码 utf-8 成功读取CSV文件
2025-06-30 16:30:49 | INFO     | src.phone_reader:_detect_phone_column:160 - 检测到电话号码列: 电话
2025-06-30 16:30:49 | INFO     | src.phone_reader:_detect_name_column:187 - 检测到姓名列: 姓名
2025-06-30 16:30:49 | INFO     | src.phone_reader:_process_dataframe:147 - 处理完成，有效号码数量: 8
2025-06-30 16:30:49 | INFO     | __main__:info:264 -   有效号码数: 8
2025-06-30 16:30:49 | INFO     | __main__:info:267 -   前5个号码:
2025-06-30 16:30:49 | INFO     | __main__:info:269 -     1. 13800138000 - 张三
2025-06-30 16:30:49 | INFO     | __main__:info:269 -     2. 13900139000 - 李四
2025-06-30 16:30:49 | INFO     | __main__:info:269 -     3. ************ - 某公司客服
2025-06-30 16:30:49 | INFO     | __main__:info:269 -     4. 13512345678 - 王五
2025-06-30 16:30:49 | INFO     | __main__:info:269 -     5. 13612345678 - 赵六
2025-06-30 16:31:06 | INFO     | __main__:main:94 - ============================================================
2025-06-30 16:31:06 | INFO     | __main__:main:95 - 电话号码标记识别系统启动
2025-06-30 16:31:06 | INFO     | __main__:main:96 - ============================================================
2025-06-30 16:31:06 | INFO     | __main__:main:105 - 初始化处理器...
2025-06-30 16:31:11 | INFO     | src.adb_controller:_init_device:38 - 设备连接成功: LGE-AN00
2025-06-30 16:31:11 | INFO     | src.main_processor:update_config:377 - 配置更新: call_timeout = 10
2025-06-30 16:31:11 | INFO     | src.main_processor:update_config:377 - 配置更新: detection_delay = 2
2025-06-30 16:31:11 | INFO     | src.main_processor:update_config:377 - 配置更新: between_calls_delay = 3
2025-06-30 16:31:11 | INFO     | __main__:main:120 - 检查设备连接...
2025-06-30 16:31:17 | INFO     | __main__:main:126 - 设备连接正常: ['adb-AQWL023228000371-LUOGfO._adb-tls-connect._tcp']
2025-06-30 16:31:17 | INFO     | __main__:main:129 - 试运行模式 - 仅读取文件，不实际拨号
2025-06-30 16:31:17 | INFO     | __main__:main:136 - 文件信息: {'file_name': 'example_phones.csv', 'file_size': 340, 'file_format': '.csv', 'is_supported': True, 'columns': ['电话', '姓名', '备注'], 'estimated_rows': '5+'}
2025-06-30 16:31:17 | INFO     | src.phone_reader:_read_csv:64 - 使用编码 utf-8 成功读取CSV文件
2025-06-30 16:31:17 | INFO     | src.phone_reader:_detect_phone_column:160 - 检测到电话号码列: 电话
2025-06-30 16:31:17 | INFO     | src.phone_reader:_detect_name_column:187 - 检测到姓名列: 姓名
2025-06-30 16:31:17 | INFO     | src.phone_reader:_process_dataframe:147 - 处理完成，有效号码数量: 8
2025-06-30 16:31:17 | INFO     | __main__:main:147 - 将处理 3 个号码
2025-06-30 16:31:17 | INFO     | __main__:main:151 -   1. 13800138000 - 张三
2025-06-30 16:31:17 | INFO     | __main__:main:151 -   2. 13900139000 - 李四
2025-06-30 16:31:17 | INFO     | __main__:main:151 -   3. ************ - 某公司客服
2025-06-30 16:31:17 | INFO     | __main__:main:156 - 试运行完成
2025-06-30 16:41:19 | INFO     | __main__:main:93 - ============================================================
2025-06-30 16:41:19 | INFO     | __main__:main:94 - 电话号码标记识别系统启动
2025-06-30 16:41:19 | INFO     | __main__:main:95 - ============================================================
2025-06-30 16:41:19 | INFO     | __main__:main:104 - 初始化处理器...
2025-06-30 16:41:20 | INFO     | src.adb_controller:_init_device:38 - 设备连接成功: LGE-AN00
2025-06-30 16:41:20 | INFO     | src.main_processor:update_config:359 - 配置更新: call_timeout = 10
2025-06-30 16:41:20 | INFO     | src.main_processor:update_config:359 - 配置更新: detection_delay = 2
2025-06-30 16:41:20 | INFO     | src.main_processor:update_config:359 - 配置更新: between_calls_delay = 3
2025-06-30 16:41:20 | INFO     | __main__:main:118 - 检查设备连接...
2025-06-30 16:41:21 | INFO     | __main__:main:124 - 设备连接正常: ['adb-AQWL023228000371-LUOGfO._adb-tls-connect._tcp']
2025-06-30 16:41:21 | INFO     | __main__:main:127 - 试运行模式 - 仅读取文件，不实际拨号
2025-06-30 16:41:21 | INFO     | __main__:main:134 - 文件信息: {'file_name': 'example_phones.csv', 'file_size': 340, 'file_format': '.csv', 'is_supported': True, 'columns': ['电话', '姓名', '备注'], 'estimated_rows': '5+'}
2025-06-30 16:41:21 | INFO     | src.phone_reader:_read_csv:64 - 使用编码 utf-8 成功读取CSV文件
2025-06-30 16:41:21 | INFO     | src.phone_reader:_detect_phone_column:160 - 检测到电话号码列: 电话
2025-06-30 16:41:21 | INFO     | src.phone_reader:_detect_name_column:187 - 检测到姓名列: 姓名
2025-06-30 16:41:21 | INFO     | src.phone_reader:_process_dataframe:147 - 处理完成，有效号码数量: 8
2025-06-30 16:41:21 | INFO     | __main__:main:145 - 将处理 2 个号码
2025-06-30 16:41:21 | INFO     | __main__:main:149 -   1. 13800138000 - 张三
2025-06-30 16:41:21 | INFO     | __main__:main:149 -   2. 13900139000 - 李四
2025-06-30 16:41:21 | INFO     | __main__:main:154 - 试运行完成
