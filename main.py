#!/usr/bin/env python3
"""
电话号码标记识别系统 - 主程序
基于ADB的电话号码标记识别工具

使用方法:
    python main.py --file phone_list.csv --output results
    python main.py --file phone_list.xlsx --phone-column "电话" --name-column "姓名"
"""

import click
import sys
import os
from pathlib import Path
from loguru import logger

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.main_processor import PhoneMarkProcessor


def setup_logging(verbose: bool = False):
    """设置日志"""
    logger.remove()
    
    # 控制台日志
    log_level = "DEBUG" if verbose else "INFO"
    logger.add(
        sys.stderr,
        level=log_level,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - <level>{message}</level>"
    )
    
    # 文件日志
    logger.add(
        "logs/phone_mark_detector.log",
        level="DEBUG",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | {name}:{function}:{line} - {message}",
        rotation="10 MB",
        retention="7 days",
        encoding="utf-8"
    )


@click.command()
@click.option('--file', '-f', 'input_file', required=True, 
              help='输入的号码文件路径 (支持CSV, Excel, TXT格式)')
@click.option('--phone-column', '-p', 'phone_column', 
              help='电话号码列名 (自动检测时可省略)')
@click.option('--name-column', '-n', 'name_column', 
              help='姓名列名 (自动检测时可省略)')
@click.option('--output', '-o', 'output_dir', default='output',
              help='输出目录 (默认: output)')
@click.option('--device', '-d', 'device_id',
              help='Android设备ID (默认使用第一个连接的设备)')

@click.option('--start', '-s', 'start_index', default=0, type=int,
              help='开始处理的索引 (默认: 0)')
@click.option('--count', '-c', 'max_count', type=int,
              help='最大处理数量 (默认: 全部)')
@click.option('--format', 'export_formats', multiple=True, 
              type=click.Choice(['csv', 'excel', 'json']),
              default=['csv', 'excel'],
              help='导出格式 (默认: csv,excel)')
@click.option('--call-timeout', type=int, default=10,
              help='拨号超时时间(秒) (默认: 10)')
@click.option('--detection-delay', type=int, default=2,
              help='检测延迟时间(秒) (默认: 2)')
@click.option('--between-calls-delay', type=int, default=3,
              help='通话间隔时间(秒) (默认: 3)')
@click.option('--verbose', '-v', is_flag=True,
              help='详细输出模式')
@click.option('--dry-run', is_flag=True,
              help='试运行模式 (不实际拨号)')
def main(input_file, phone_column, name_column, output_dir, device_id,
         start_index, max_count, export_formats,
         call_timeout, detection_delay, between_calls_delay,
         verbose, dry_run):
    """
    电话号码标记识别系统
    
    通过ADB控制Android设备拨打电话，识别号码标记信息并生成报告。
    """
    
    # 设置日志
    setup_logging(verbose)
    
    # 创建必要目录
    Path("logs").mkdir(exist_ok=True)
    Path(output_dir).mkdir(exist_ok=True)
    
    logger.info("=" * 60)
    logger.info("电话号码标记识别系统启动")
    logger.info("=" * 60)
    
    try:
        # 验证输入文件
        input_path = Path(input_file)
        if not input_path.exists():
            raise click.ClickException(f"输入文件不存在: {input_file}")
        
        # 初始化处理器
        logger.info("初始化处理器...")
        processor = PhoneMarkProcessor(
            device_id=device_id,
            output_dir=output_dir
        )
        
        # 更新配置
        processor.update_config(
            call_timeout=call_timeout,
            detection_delay=detection_delay,
            between_calls_delay=between_calls_delay
        )
        
        # 检查设备连接
        logger.info("检查设备连接...")
        device_info = processor.get_device_info()
        
        if not device_info.get('device_connected', False):
            raise click.ClickException("Android设备未连接，请检查ADB连接")
        
        logger.info(f"设备连接正常: {device_info.get('connected_devices', [])}")
        
        if dry_run:
            logger.info("试运行模式 - 仅读取文件，不实际拨号")
            
            # 读取并显示文件信息
            from src.phone_reader import PhoneNumberReader
            reader = PhoneNumberReader()
            
            file_info = reader.get_file_info(input_file)
            logger.info(f"文件信息: {file_info}")
            
            phone_numbers = reader.read_phone_numbers(
                input_file, phone_column, name_column
            )
            
            if start_index > 0:
                phone_numbers = phone_numbers[start_index:]
            if max_count:
                phone_numbers = phone_numbers[:max_count]
            
            logger.info(f"将处理 {len(phone_numbers)} 个号码")
            
            # 显示前几个号码
            for i, phone_info in enumerate(phone_numbers[:5]):
                logger.info(f"  {i+1}. {phone_info.get('phone', '')} - {phone_info.get('name', '')}")
            
            if len(phone_numbers) > 5:
                logger.info(f"  ... 还有 {len(phone_numbers) - 5} 个号码")
            
            logger.info("试运行完成")
            return
        
        # 处理电话号码列表
        logger.info("开始处理电话号码...")
        processing_result = processor.process_phone_list(
            input_file,
            phone_column=phone_column,
            name_column=name_column,
            start_index=start_index,
            max_count=max_count
        )
        
        results = processing_result['results']
        summary = processing_result['summary']
        
        # 显示处理摘要
        logger.info("处理完成!")
        logger.info(f"总处理数量: {summary['total_processed']}")
        logger.info(f"成功数量: {summary['success_count']}")
        logger.info(f"失败数量: {summary['failed_count']}")
        logger.info(f"成功率: {summary['success_rate']:.2%}")
        logger.info(f"标记数量: {summary['marked_count']}")
        logger.info(f"标记率: {summary['mark_rate']:.2%}")
        logger.info(f"平均处理时间: {summary['average_processing_time']:.2f}秒")
        
        # 导出结果
        logger.info("导出结果...")
        exported_files = processor.export_results(results, list(export_formats))
        
        logger.info("导出完成:")
        for format_type, file_path in exported_files.items():
            if isinstance(file_path, list):
                logger.info(f"  {format_type}: {len(file_path)} 个文件")
                for fp in file_path:
                    logger.info(f"    - {fp}")
            else:
                logger.info(f"  {format_type}: {file_path}")
        
        logger.info("=" * 60)
        logger.info("程序执行完成!")
        logger.info("=" * 60)
        
    except click.ClickException:
        raise
    except KeyboardInterrupt:
        logger.warning("用户中断程序")
        sys.exit(1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        if verbose:
            logger.exception("详细错误信息:")
        sys.exit(1)


@click.group()
def cli():
    """电话号码标记识别系统命令行工具"""
    pass


@cli.command()
def devices():
    """列出连接的Android设备"""
    setup_logging()
    
    try:
        from src.adb_controller import ADBController
        controller = ADBController()
        devices = controller.get_connected_devices()
        
        if devices:
            logger.info("已连接的设备:")
            for device in devices:
                logger.info(f"  - {device}")
        else:
            logger.warning("未找到连接的设备")
            
    except Exception as e:
        logger.error(f"获取设备列表失败: {e}")


@cli.command()
@click.argument('file_path')
def info(file_path):
    """显示号码文件信息"""
    setup_logging()
    
    try:
        from src.phone_reader import PhoneNumberReader
        reader = PhoneNumberReader()
        
        if not Path(file_path).exists():
            raise click.ClickException(f"文件不存在: {file_path}")
        
        file_info = reader.get_file_info(file_path)
        
        logger.info("文件信息:")
        logger.info(f"  文件名: {file_info.get('file_name', '')}")
        logger.info(f"  文件大小: {file_info.get('file_size', 0)} 字节")
        logger.info(f"  文件格式: {file_info.get('file_format', '')}")
        logger.info(f"  是否支持: {'是' if file_info.get('is_supported', False) else '否'}")
        
        if 'columns' in file_info:
            logger.info(f"  列名: {file_info['columns']}")
        
        # 尝试读取前几行
        phone_numbers = reader.read_phone_numbers(file_path)
        logger.info(f"  有效号码数: {len(phone_numbers)}")
        
        if phone_numbers:
            logger.info("  前5个号码:")
            for i, phone_info in enumerate(phone_numbers[:5]):
                logger.info(f"    {i+1}. {phone_info.get('phone', '')} - {phone_info.get('name', '')}")
        
    except Exception as e:
        logger.error(f"获取文件信息失败: {e}")


if __name__ == '__main__':
    # 如果没有参数，显示帮助
    if len(sys.argv) == 1:
        cli(['--help'])
    else:
        # 检查是否是子命令
        if len(sys.argv) > 1 and sys.argv[1] in ['devices', 'info']:
            cli()
        else:
            main()
