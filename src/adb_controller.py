"""
ADB通信控制模块
负责与Android设备的通信，执行拨号和挂断操作
"""

import subprocess
import time
import re
from typing import Optional, List, Dict, Any
from loguru import logger
import uiautomator2 as u2


class ADBController:
    """ADB设备控制器"""
    
    def __init__(self, device_id: Optional[str] = None):
        """
        初始化ADB控制器
        
        Args:
            device_id: 设备ID，如果为None则使用第一个连接的设备
        """
        self.device_id = device_id
        self.device = None
        self._init_device()
    
    def _init_device(self):
        """初始化设备连接"""
        try:
            if self.device_id:
                self.device = u2.connect(self.device_id)
            else:
                self.device = u2.connect()
            
            # 检查设备连接状态
            device_info = self.device.info
            logger.info(f"设备连接成功: {device_info.get('productName', 'Unknown')}")
            
        except Exception as e:
            logger.error(f"设备连接失败: {e}")
            raise
    
    def check_device_connection(self) -> bool:
        """检查设备连接状态"""
        try:
            return self.device.info is not None
        except:
            return False
    
    def get_connected_devices(self) -> List[str]:
        """获取已连接的设备列表"""
        try:
            result = subprocess.run(['adb', 'devices'], 
                                  capture_output=True, text=True)
            devices = []
            for line in result.stdout.split('\n')[1:]:
                if line.strip() and 'device' in line:
                    device_id = line.split('\t')[0]
                    devices.append(device_id)
            return devices
        except Exception as e:
            logger.error(f"获取设备列表失败: {e}")
            return []
    
    def make_call(self, phone_number: str) -> bool:
        """
        拨打电话
        
        Args:
            phone_number: 电话号码
            
        Returns:
            bool: 拨号是否成功
        """
        try:
            # 清理号码格式
            clean_number = re.sub(r'[^\d+]', '', phone_number)
            logger.info(f"开始拨号: {clean_number}")
            
            # 使用ADB命令拨号
            cmd = f"am start -a android.intent.action.CALL -d tel:{clean_number}"
            result = self.device.shell(cmd)
            
            # 等待拨号界面出现
            time.sleep(2)
            
            # 检查是否成功进入拨号界面
            if self._is_in_call_screen():
                logger.info(f"拨号成功: {clean_number}")
                return True
            else:
                logger.warning(f"拨号可能失败: {clean_number}")
                return False
                
        except Exception as e:
            logger.error(f"拨号失败 {phone_number}: {e}")
            return False
    
    def end_call(self) -> bool:
        """
        挂断电话
        
        Returns:
            bool: 挂断是否成功
        """
        try:
            # 方法1: 使用按键挂断
            self.device.press("endcall")
            time.sleep(1)
            
            # 方法2: 如果方法1失败，尝试点击挂断按钮
            if self._is_in_call_screen():
                # 查找挂断按钮并点击
                end_button = self.device(description="结束通话") or \
                           self.device(description="End call") or \
                           self.device(resourceId="com.android.dialer:id/dialpad_floating_end_call_button")
                
                if end_button.exists:
                    end_button.click()
                    time.sleep(1)
            
            # 验证是否成功挂断
            if not self._is_in_call_screen():
                logger.info("挂断成功")
                return True
            else:
                logger.warning("挂断可能失败")
                return False
                
        except Exception as e:
            logger.error(f"挂断失败: {e}")
            return False
    
    def _is_in_call_screen(self) -> bool:
        """检查是否在通话界面"""
        try:
            # 检查常见的通话界面元素
            call_indicators = [
                self.device(resourceId="com.android.dialer:id/contactgrid_bottom_timer"),
                self.device(resourceId="com.android.incallui:id/answer_and_dialpad_container"),
                self.device(description="通话中"),
                self.device(description="In call"),
                self.device(text="正在呼叫"),
                self.device(text="Calling")
            ]
            
            return any(indicator.exists for indicator in call_indicators)
            
        except Exception as e:
            logger.error(f"检查通话状态失败: {e}")
            return False
    
    def capture_screen(self, save_path: str) -> bool:
        """
        截取屏幕
        
        Args:
            save_path: 保存路径
            
        Returns:
            bool: 截图是否成功
        """
        try:
            self.device.screenshot(save_path)
            logger.debug(f"截图保存至: {save_path}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False
    
    def get_current_activity(self) -> Optional[str]:
        """获取当前Activity"""
        try:
            info = self.device.app_current()
            return info.get('activity', None)
        except Exception as e:
            logger.error(f"获取当前Activity失败: {e}")
            return None
    
    def wait_for_call_connection(self, timeout: int = 10) -> bool:
        """
        等待电话接通
        
        Args:
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否检测到接通
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查是否有接通的迹象
                connected_indicators = [
                    self.device(text="已接通"),
                    self.device(text="Connected"),
                    self.device(resourceId="com.android.dialer:id/contactgrid_bottom_timer"),
                ]
                
                if any(indicator.exists for indicator in connected_indicators):
                    logger.info("检测到电话接通")
                    return True
                    
                time.sleep(0.5)
                
            except Exception as e:
                logger.error(f"检查接通状态失败: {e}")
                
        logger.info("等待接通超时")
        return False
