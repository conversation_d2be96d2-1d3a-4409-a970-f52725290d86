"""
数据输出和统计模块
负责将检测结果导出为CSV、Excel等格式，并生成统计报告
"""

import pandas as pd
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger
import matplotlib.pyplot as plt
import seaborn as sns


class DataExporter:
    """数据导出器"""
    
    def __init__(self, output_dir: str = "output"):
        """
        初始化数据导出器
        
        Args:
            output_dir: 输出目录
        """
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS']
        plt.rcParams['axes.unicode_minus'] = False
    
    def export_to_csv(self, results: List[Dict[str, Any]], 
                     filename: Optional[str] = None) -> str:
        """
        导出结果到CSV文件
        
        Args:
            results: 检测结果列表
            filename: 文件名（可选）
            
        Returns:
            str: 输出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"phone_marks_{timestamp}.csv"
        
        filepath = self.output_dir / filename
        
        try:
            # 转换为DataFrame
            df = self._results_to_dataframe(results)
            
            # 导出CSV
            df.to_csv(filepath, index=False, encoding='utf-8-sig')
            logger.info(f"CSV文件已导出: {filepath}")
            
            return str(filepath)
            
        except Exception as e:
            logger.error(f"导出CSV失败: {e}")
            raise
    
    def export_to_excel(self, results: List[Dict[str, Any]], 
                       filename: Optional[str] = None) -> str:
        """
        导出结果到Excel文件
        
        Args:
            results: 检测结果列表
            filename: 文件名（可选）
            
        Returns:
            str: 输出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"phone_marks_{timestamp}.xlsx"
        
        filepath = self.output_dir / filename
        
        try:
            # 转换为DataFrame
            df = self._results_to_dataframe(results)
            
            # 创建Excel写入器
            with pd.ExcelWriter(filepath, engine='openpyxl') as writer:
                # 写入主数据
                df.to_excel(writer, sheet_name='检测结果', index=False)
                
                # 生成统计数据
                stats_df = self._generate_statistics(results)
                stats_df.to_excel(writer, sheet_name='统计分析', index=False)
                
                # 格式化Excel
                self._format_excel(writer, df, stats_df)
            
            logger.info(f"Excel文件已导出: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"导出Excel失败: {e}")
            raise
    
    def export_to_json(self, results: List[Dict[str, Any]], 
                      filename: Optional[str] = None) -> str:
        """
        导出结果到JSON文件
        
        Args:
            results: 检测结果列表
            filename: 文件名（可选）
            
        Returns:
            str: 输出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"phone_marks_{timestamp}.json"
        
        filepath = self.output_dir / filename
        
        try:
            # 准备导出数据
            export_data = {
                'export_time': datetime.now().isoformat(),
                'total_count': len(results),
                'results': results
            }
            
            # 写入JSON文件
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"JSON文件已导出: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"导出JSON失败: {e}")
            raise
    
    def _results_to_dataframe(self, results: List[Dict[str, Any]]) -> pd.DataFrame:
        """将结果转换为DataFrame"""
        if not results:
            return pd.DataFrame()
        
        # 展平结果数据
        flattened_results = []
        for result in results:
            flat_result = {
                '电话号码': result.get('phone_number', ''),
                '姓名': result.get('name', ''),
                '是否有标记': '是' if result.get('has_mark', False) else '否',
                '标记类型': self._translate_mark_type(result.get('mark_type', 'unknown')),
                '标记人数': result.get('mark_count', 0),
                '描述信息': result.get('description', ''),
                '置信度': f"{result.get('confidence', 0):.2%}",
                '检测时间': self._format_timestamp(result.get('timestamp', 0)),
                '拨号状态': result.get('call_status', '未知'),
                '处理时长(秒)': result.get('processing_time', 0)
            }
            flattened_results.append(flat_result)
        
        return pd.DataFrame(flattened_results)
    
    def _translate_mark_type(self, mark_type: str) -> str:
        """翻译标记类型"""
        translations = {
            'spam': '骚扰电话',
            'delivery': '快递外卖',
            'business': '商业客服',
            'unknown': '未知',
            'normal': '正常号码'
        }
        return translations.get(mark_type, mark_type)
    
    def _format_timestamp(self, timestamp: float) -> str:
        """格式化时间戳"""
        if timestamp == 0:
            return ''
        return datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
    
    def _generate_statistics(self, results: List[Dict[str, Any]]) -> pd.DataFrame:
        """生成统计数据"""
        if not results:
            return pd.DataFrame()
        
        total_count = len(results)
        marked_count = sum(1 for r in results if r.get('has_mark', False))
        
        # 按标记类型统计
        type_stats = {}
        for result in results:
            mark_type = self._translate_mark_type(result.get('mark_type', 'unknown'))
            type_stats[mark_type] = type_stats.get(mark_type, 0) + 1
        
        # 创建统计DataFrame
        stats_data = [
            ['总号码数', total_count],
            ['有标记数', marked_count],
            ['无标记数', total_count - marked_count],
            ['标记率', f"{marked_count/total_count:.2%}" if total_count > 0 else "0%"],
            ['', ''],  # 空行
            ['标记类型分布', ''],
        ]
        
        for mark_type, count in type_stats.items():
            percentage = f"{count/total_count:.2%}" if total_count > 0 else "0%"
            stats_data.append([f"  {mark_type}", f"{count} ({percentage})"])
        
        return pd.DataFrame(stats_data, columns=['统计项目', '数值'])
    
    def _format_excel(self, writer, main_df: pd.DataFrame, stats_df: pd.DataFrame):
        """格式化Excel文件"""
        try:
            from openpyxl.styles import Font, PatternFill, Alignment
            from openpyxl.utils.dataframe import dataframe_to_rows
            
            # 获取工作簿和工作表
            workbook = writer.book
            main_sheet = writer.sheets['检测结果']
            stats_sheet = writer.sheets['统计分析']
            
            # 设置标题样式
            title_font = Font(bold=True, size=12)
            header_fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
            
            # 格式化主数据表
            for cell in main_sheet[1]:
                cell.font = title_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center')
            
            # 格式化统计表
            for cell in stats_sheet[1]:
                cell.font = title_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal='center')
            
            # 自动调整列宽
            for sheet in [main_sheet, stats_sheet]:
                for column in sheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    adjusted_width = min(max_length + 2, 50)
                    sheet.column_dimensions[column_letter].width = adjusted_width
            
        except ImportError:
            logger.warning("openpyxl不可用，跳过Excel格式化")
        except Exception as e:
            logger.warning(f"Excel格式化失败: {e}")
    
    def generate_charts(self, results: List[Dict[str, Any]], 
                       output_prefix: Optional[str] = None) -> List[str]:
        """
        生成统计图表
        
        Args:
            results: 检测结果列表
            output_prefix: 输出文件前缀
            
        Returns:
            List[str]: 生成的图表文件路径列表
        """
        if not results:
            return []
        
        if not output_prefix:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_prefix = f"charts_{timestamp}"
        
        chart_files = []
        
        try:
            # 1. 标记类型分布饼图
            pie_chart_path = self._create_pie_chart(results, output_prefix)
            if pie_chart_path:
                chart_files.append(pie_chart_path)
            
            # 2. 标记数量分布柱状图
            bar_chart_path = self._create_bar_chart(results, output_prefix)
            if bar_chart_path:
                chart_files.append(bar_chart_path)
            
            # 3. 置信度分布直方图
            hist_chart_path = self._create_histogram(results, output_prefix)
            if hist_chart_path:
                chart_files.append(hist_chart_path)
            
        except Exception as e:
            logger.error(f"生成图表失败: {e}")
        
        return chart_files
    
    def _create_pie_chart(self, results: List[Dict[str, Any]], prefix: str) -> Optional[str]:
        """创建标记类型分布饼图"""
        try:
            # 统计标记类型
            type_counts = {}
            for result in results:
                mark_type = self._translate_mark_type(result.get('mark_type', 'unknown'))
                type_counts[mark_type] = type_counts.get(mark_type, 0) + 1
            
            if not type_counts:
                return None
            
            # 创建饼图
            plt.figure(figsize=(10, 8))
            plt.pie(type_counts.values(), labels=type_counts.keys(), autopct='%1.1f%%')
            plt.title('电话号码标记类型分布', fontsize=16, fontweight='bold')
            
            # 保存图表
            chart_path = self.output_dir / f"{prefix}_mark_types.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"饼图已生成: {chart_path}")
            return str(chart_path)
            
        except Exception as e:
            logger.error(f"创建饼图失败: {e}")
            return None
    
    def _create_bar_chart(self, results: List[Dict[str, Any]], prefix: str) -> Optional[str]:
        """创建标记数量分布柱状图"""
        try:
            # 统计标记数量分布
            count_ranges = {'0': 0, '1-10': 0, '11-50': 0, '51-100': 0, '100+': 0}
            
            for result in results:
                count = result.get('mark_count', 0)
                if count == 0:
                    count_ranges['0'] += 1
                elif count <= 10:
                    count_ranges['1-10'] += 1
                elif count <= 50:
                    count_ranges['11-50'] += 1
                elif count <= 100:
                    count_ranges['51-100'] += 1
                else:
                    count_ranges['100+'] += 1
            
            # 创建柱状图
            plt.figure(figsize=(12, 6))
            bars = plt.bar(count_ranges.keys(), count_ranges.values())
            plt.title('标记数量分布', fontsize=16, fontweight='bold')
            plt.xlabel('标记人数范围')
            plt.ylabel('号码数量')
            
            # 添加数值标签
            for bar in bars:
                height = bar.get_height()
                plt.text(bar.get_x() + bar.get_width()/2., height,
                        f'{int(height)}', ha='center', va='bottom')
            
            # 保存图表
            chart_path = self.output_dir / f"{prefix}_mark_counts.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"柱状图已生成: {chart_path}")
            return str(chart_path)
            
        except Exception as e:
            logger.error(f"创建柱状图失败: {e}")
            return None
    
    def _create_histogram(self, results: List[Dict[str, Any]], prefix: str) -> Optional[str]:
        """创建置信度分布直方图"""
        try:
            # 提取置信度数据
            confidences = [result.get('confidence', 0) for result in results]
            
            if not confidences:
                return None
            
            # 创建直方图
            plt.figure(figsize=(10, 6))
            plt.hist(confidences, bins=20, alpha=0.7, edgecolor='black')
            plt.title('检测置信度分布', fontsize=16, fontweight='bold')
            plt.xlabel('置信度')
            plt.ylabel('频次')
            plt.grid(True, alpha=0.3)
            
            # 保存图表
            chart_path = self.output_dir / f"{prefix}_confidence.png"
            plt.savefig(chart_path, dpi=300, bbox_inches='tight')
            plt.close()
            
            logger.info(f"直方图已生成: {chart_path}")
            return str(chart_path)
            
        except Exception as e:
            logger.error(f"创建直方图失败: {e}")
            return None
    
    def create_summary_report(self, results: List[Dict[str, Any]], 
                            filename: Optional[str] = None) -> str:
        """
        创建汇总报告
        
        Args:
            results: 检测结果列表
            filename: 报告文件名
            
        Returns:
            str: 报告文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"summary_report_{timestamp}.txt"
        
        filepath = self.output_dir / filename
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write("电话号码标记检测汇总报告\n")
                f.write("=" * 50 + "\n\n")
                
                # 基本统计
                total_count = len(results)
                marked_count = sum(1 for r in results if r.get('has_mark', False))
                
                f.write(f"检测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"总检测号码数: {total_count}\n")
                f.write(f"有标记号码数: {marked_count}\n")
                f.write(f"无标记号码数: {total_count - marked_count}\n")
                f.write(f"标记率: {marked_count/total_count:.2%}\n\n" if total_count > 0 else "标记率: 0%\n\n")
                
                # 标记类型统计
                f.write("标记类型分布:\n")
                f.write("-" * 20 + "\n")
                
                type_stats = {}
                for result in results:
                    mark_type = self._translate_mark_type(result.get('mark_type', 'unknown'))
                    type_stats[mark_type] = type_stats.get(mark_type, 0) + 1
                
                for mark_type, count in sorted(type_stats.items()):
                    percentage = f"{count/total_count:.2%}" if total_count > 0 else "0%"
                    f.write(f"{mark_type}: {count} ({percentage})\n")
                
                # 高风险号码
                f.write("\n高风险号码 (骚扰电话):\n")
                f.write("-" * 30 + "\n")
                
                spam_numbers = [r for r in results if r.get('mark_type') == 'spam']
                if spam_numbers:
                    for result in sorted(spam_numbers, key=lambda x: x.get('mark_count', 0), reverse=True)[:10]:
                        f.write(f"{result.get('phone_number', '')}: {result.get('mark_count', 0)}人标记\n")
                else:
                    f.write("无骚扰电话\n")
            
            logger.info(f"汇总报告已生成: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"生成汇总报告失败: {e}")
            raise
