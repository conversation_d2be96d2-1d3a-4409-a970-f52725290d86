"""
号码标记识别模块
通过屏幕截图和OCR技术识别电话号码的标记信息
"""

import cv2
import numpy as np
import pytesseract
import re
import time
from typing import Dict, List, Optional, Tuple
from PIL import Image
from loguru import logger
from pathlib import Path


class MarkDetector:
    """号码标记检测器"""
    
    def __init__(self, tesseract_path: Optional[str] = None):
        """
        初始化标记检测器
        
        Args:
            tesseract_path: Tesseract可执行文件路径
        """
        if tesseract_path:
            pytesseract.pytesseract.tesseract_cmd = tesseract_path
        
        # 常见标记关键词
        self.mark_keywords = {
            'spam': ['骚扰', '推销', '广告', '诈骗', '垃圾', 'spam', '骚扰电话'],
            'delivery': ['快递', '外卖', '送餐', '配送', 'delivery', '快递员'],
            'business': ['客服', '服务', '公司', '企业', '商家', 'business'],
            'unknown': ['未知', '陌生', 'unknown', '未标记'],
            'normal': ['正常', '朋友', '同事', 'normal', '联系人']
        }
        
        # 数字提取模式
        self.number_patterns = [
            r'(\d+)\s*人标记',
            r'(\d+)\s*次标记',
            r'标记\s*(\d+)\s*次',
            r'(\d+)\s*用户标记',
            r'(\d+)\s*个用户',
        ]
    
    def detect_marks_from_screenshot(self, image_path: str, 
                                   phone_number: str) -> Dict[str, any]:
        """
        从截图中检测号码标记信息
        
        Args:
            image_path: 截图文件路径
            phone_number: 电话号码
            
        Returns:
            Dict: 包含标记信息的字典
        """
        try:
            # 读取图像
            image = cv2.imread(image_path)
            if image is None:
                logger.error(f"无法读取图像: {image_path}")
                return self._create_empty_result(phone_number)
            
            # 预处理图像
            processed_image = self._preprocess_image(image)
            
            # OCR识别文本
            text = self._extract_text(processed_image)
            logger.debug(f"OCR识别文本: {text}")
            
            # 分析标记信息
            mark_info = self._analyze_mark_text(text, phone_number)
            
            return mark_info
            
        except Exception as e:
            logger.error(f"检测标记失败 {image_path}: {e}")
            return self._create_empty_result(phone_number)
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """预处理图像以提高OCR准确性"""
        # 转换为灰度图
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 高斯模糊去噪
        blurred = cv2.GaussianBlur(gray, (3, 3), 0)
        
        # 自适应阈值二值化
        binary = cv2.adaptiveThreshold(
            blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
            cv2.THRESH_BINARY, 11, 2
        )
        
        # 形态学操作去除噪点
        kernel = np.ones((2, 2), np.uint8)
        cleaned = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
        
        return cleaned
    
    def _extract_text(self, image: np.ndarray) -> str:
        """使用OCR提取图像中的文本"""
        try:
            # 配置OCR参数
            config = '--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ一二三四五六七八九十百千万亿人次个用户标记骚扰推销广告诈骗垃圾快递外卖送餐配送客服服务公司企业商家未知陌生正常朋友同事联系'
            
            # 执行OCR
            text = pytesseract.image_to_string(image, lang='chi_sim+eng', config=config)
            
            # 清理文本
            text = re.sub(r'\s+', ' ', text.strip())
            
            return text
            
        except Exception as e:
            logger.error(f"OCR文本提取失败: {e}")
            return ""
    
    def _analyze_mark_text(self, text: str, phone_number: str) -> Dict[str, any]:
        """分析OCR文本中的标记信息"""
        result = self._create_empty_result(phone_number)
        
        if not text:
            return result
        
        # 检测标记类型
        mark_type = self._detect_mark_type(text)
        result['mark_type'] = mark_type
        
        # 提取标记数量
        mark_count = self._extract_mark_count(text)
        result['mark_count'] = mark_count
        
        # 提取详细描述
        description = self._extract_description(text)
        result['description'] = description
        
        # 设置是否有标记
        result['has_mark'] = mark_type != 'unknown' or mark_count > 0
        
        # 计算置信度
        result['confidence'] = self._calculate_confidence(text, mark_type, mark_count)
        
        logger.info(f"检测结果: {phone_number} - {mark_type} ({mark_count}人标记)")
        
        return result
    
    def _detect_mark_type(self, text: str) -> str:
        """检测标记类型"""
        text_lower = text.lower()
        
        # 按优先级检查关键词
        for mark_type, keywords in self.mark_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return mark_type
        
        return 'unknown'
    
    def _extract_mark_count(self, text: str) -> int:
        """提取标记数量"""
        for pattern in self.number_patterns:
            matches = re.findall(pattern, text)
            if matches:
                try:
                    return int(matches[0])
                except ValueError:
                    continue
        
        # 尝试提取任何数字
        numbers = re.findall(r'\d+', text)
        if numbers:
            # 返回最大的数字（通常是标记数量）
            return max(int(num) for num in numbers if int(num) < 10000)
        
        return 0
    
    def _extract_description(self, text: str) -> str:
        """提取详细描述"""
        # 清理和格式化文本
        description = re.sub(r'\s+', ' ', text.strip())
        
        # 限制长度
        if len(description) > 100:
            description = description[:100] + "..."
        
        return description
    
    def _calculate_confidence(self, text: str, mark_type: str, mark_count: int) -> float:
        """计算检测置信度"""
        confidence = 0.0
        
        # 基于文本长度
        if len(text) > 10:
            confidence += 0.3
        
        # 基于标记类型
        if mark_type != 'unknown':
            confidence += 0.4
        
        # 基于标记数量
        if mark_count > 0:
            confidence += 0.3
        
        # 基于关键词匹配度
        keyword_matches = 0
        for keywords in self.mark_keywords.values():
            for keyword in keywords:
                if keyword.lower() in text.lower():
                    keyword_matches += 1
        
        if keyword_matches > 0:
            confidence += min(0.2 * keyword_matches, 0.5)
        
        return min(confidence, 1.0)
    
    def _create_empty_result(self, phone_number: str) -> Dict[str, any]:
        """创建空的检测结果"""
        return {
            'phone_number': phone_number,
            'has_mark': False,
            'mark_type': 'unknown',
            'mark_count': 0,
            'description': '',
            'confidence': 0.0,
            'timestamp': time.time()
        }
    
    def detect_marks_from_ui_elements(self, device, phone_number: str) -> Dict[str, any]:
        """
        通过UI元素检测标记信息（备用方法）
        
        Args:
            device: uiautomator2设备对象
            phone_number: 电话号码
            
        Returns:
            Dict: 标记信息
        """
        try:
            # 查找常见的标记UI元素
            mark_elements = [
                device(textContains="标记"),
                device(textContains="骚扰"),
                device(textContains="快递"),
                device(textContains="推销"),
                device(descriptionContains="标记"),
            ]
            
            result = self._create_empty_result(phone_number)
            
            for element in mark_elements:
                if element.exists:
                    text = element.get_text()
                    if text:
                        mark_info = self._analyze_mark_text(text, phone_number)
                        if mark_info['has_mark']:
                            return mark_info
            
            return result
            
        except Exception as e:
            logger.error(f"UI元素检测失败: {e}")
            return self._create_empty_result(phone_number)
    
    def save_screenshot_with_annotations(self, image_path: str, 
                                       mark_info: Dict[str, any],
                                       output_path: str) -> bool:
        """
        保存带有标注的截图
        
        Args:
            image_path: 原始截图路径
            mark_info: 标记信息
            output_path: 输出路径
            
        Returns:
            bool: 是否保存成功
        """
        try:
            image = cv2.imread(image_path)
            if image is None:
                return False
            
            # 添加标注文本
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.7
            color = (0, 255, 0) if mark_info['has_mark'] else (0, 0, 255)
            thickness = 2
            
            # 准备标注文本
            annotation = f"Type: {mark_info['mark_type']}, Count: {mark_info['mark_count']}"
            
            # 添加文本到图像
            cv2.putText(image, annotation, (10, 30), font, font_scale, color, thickness)
            
            # 保存图像
            cv2.imwrite(output_path, image)
            logger.debug(f"保存标注截图: {output_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存标注截图失败: {e}")
            return False
