"""
号码标记识别模块
通过拨号界面UI元素直接获取电话号码的标记信息
"""

import re
import time
from typing import Dict, List, Optional, Tuple
from loguru import logger


class MarkDetector:
    """号码标记检测器"""

    def __init__(self):
        """初始化标记检测器"""

        # 常见标记关键词映射
        self.mark_keywords = {
            'spam': ['骚扰', '推销', '广告', '诈骗', '垃圾', '营销', '骚扰电话', '疑似诈骗'],
            'delivery': ['快递', '外卖', '送餐', '配送', '快递员', '外卖员', '配送员'],
            'business': ['客服', '服务', '公司', '企业', '商家', '官方', '客服热线'],
            'taxi': ['出租车', '网约车', '滴滴', '司机'],
            'normal': ['正常', '朋友', '同事', '联系人'],
            'unknown': ['未知', '陌生', '未标记']
        }

        # 标记数量提取模式
        self.number_patterns = [
            r'(\d+)\s*人标记',
            r'(\d+)\s*次标记',
            r'标记\s*(\d+)\s*次',
            r'(\d+)\s*用户标记',
            r'(\d+)\s*个用户',
            r'(\d+)\s*人认为',
            r'共\s*(\d+)\s*人',
        ]

        # 拨号界面常见的标记显示元素
        self.mark_ui_selectors = [
            # 通用标记显示
            'textContains("标记")',
            'textContains("骚扰")',
            'textContains("快递")',
            'textContains("推销")',
            'textContains("广告")',
            'textContains("客服")',
            'textContains("疑似")',

            # 具体的UI元素ID（需要根据不同手机品牌调整）
            'resourceId("com.android.dialer:id/caller_id_and_number")',
            'resourceId("com.android.dialer:id/contact_name")',
            'resourceId("com.miui.securitycenter:id/am_textview_tag")',  # 小米
            'resourceId("com.huawei.contacts:id/tag_text")',  # 华为
            'className("android.widget.TextView")',
        ]
    
    def detect_marks_from_dialer_ui(self, device, phone_number: str) -> Dict[str, any]:
        """
        从拨号界面UI直接获取号码标记信息

        Args:
            device: uiautomator2设备对象
            phone_number: 电话号码

        Returns:
            Dict: 包含标记信息的字典
        """
        try:
            logger.info(f"开始检测号码标记: {phone_number}")

            # 等待拨号界面稳定
            time.sleep(2)

            # 获取所有可能包含标记信息的文本
            mark_texts = self._extract_mark_texts_from_ui(device)

            # 分析标记信息
            mark_info = self._analyze_mark_texts(mark_texts, phone_number)

            logger.info(f"标记检测完成: {phone_number} -> {mark_info['mark_type']} ({mark_info['mark_count']}人)")

            return mark_info

        except Exception as e:
            logger.error(f"UI标记检测失败 {phone_number}: {e}")
            return self._create_empty_result(phone_number)
    
    def _extract_mark_texts_from_ui(self, device) -> List[str]:
        """从UI界面提取所有可能的标记文本"""
        mark_texts = []

        try:
            # 获取屏幕上所有文本元素
            all_texts = device.dump_hierarchy()

            # 查找包含标记关键词的文本
            for keyword_list in self.mark_keywords.values():
                for keyword in keyword_list:
                    if keyword in all_texts:
                        # 提取包含关键词的完整文本
                        elements = device(textContains=keyword)
                        for element in elements:
                            if element.exists:
                                text = element.get_text()
                                if text and text not in mark_texts:
                                    mark_texts.append(text)
                                    logger.debug(f"找到标记文本: {text}")

            # 查找数字相关的标记文本
            number_elements = device(textMatches=r'.*\d+.*人.*')
            for element in number_elements:
                if element.exists:
                    text = element.get_text()
                    if text and text not in mark_texts:
                        mark_texts.append(text)
                        logger.debug(f"找到数字标记文本: {text}")

            # 查找特定的UI元素
            specific_selectors = [
                'resourceId("com.android.dialer:id/caller_id_and_number")',
                'resourceId("com.miui.securitycenter:id/am_textview_tag")',
                'resourceId("com.huawei.contacts:id/tag_text")',
            ]

            for selector in specific_selectors:
                try:
                    elements = device(resourceId=selector.split('"')[1])
                    for element in elements:
                        if element.exists:
                            text = element.get_text()
                            if text and text not in mark_texts:
                                mark_texts.append(text)
                                logger.debug(f"找到特定元素文本: {text}")
                except:
                    continue

        except Exception as e:
            logger.error(f"提取UI文本失败: {e}")

        return mark_texts
    
    def _analyze_mark_texts(self, mark_texts: List[str], phone_number: str) -> Dict[str, any]:
        """分析提取的标记文本"""
        result = self._create_empty_result(phone_number)

        if not mark_texts:
            return result

        # 合并所有文本进行分析
        combined_text = ' '.join(mark_texts)
        logger.debug(f"合并的标记文本: {combined_text}")

        # 检测标记类型
        mark_type = self._detect_mark_type(combined_text)
        result['mark_type'] = mark_type

        # 提取标记数量
        mark_count = self._extract_mark_count(combined_text)
        result['mark_count'] = mark_count

        # 提取详细描述
        description = self._extract_description(combined_text, mark_texts)
        result['description'] = description

        # 设置是否有标记
        result['has_mark'] = mark_type != 'unknown' or mark_count > 0

        # 计算置信度
        result['confidence'] = self._calculate_confidence(combined_text, mark_type, mark_count)

        return result
    
    def _detect_mark_type(self, text: str) -> str:
        """检测标记类型"""
        text_lower = text.lower()
        
        # 按优先级检查关键词
        for mark_type, keywords in self.mark_keywords.items():
            for keyword in keywords:
                if keyword.lower() in text_lower:
                    return mark_type
        
        return 'unknown'
    
    def _extract_mark_count(self, text: str) -> int:
        """提取标记数量"""
        for pattern in self.number_patterns:
            matches = re.findall(pattern, text)
            if matches:
                try:
                    return int(matches[0])
                except ValueError:
                    continue
        
        # 尝试提取任何数字
        numbers = re.findall(r'\d+', text)
        if numbers:
            # 返回最大的数字（通常是标记数量）
            return max(int(num) for num in numbers if int(num) < 10000)
        
        return 0
    
    def _extract_description(self, combined_text: str, original_texts: List[str]) -> str:
        """提取详细描述"""
        # 优先使用最相关的原始文本
        for text in original_texts:
            if any(keyword in text.lower() for keywords in self.mark_keywords.values() for keyword in keywords):
                return text.strip()

        # 如果没有找到相关文本，使用合并文本
        description = re.sub(r'\s+', ' ', combined_text.strip())

        # 限制长度
        if len(description) > 100:
            description = description[:100] + "..."

        return description
    
    def _calculate_confidence(self, text: str, mark_type: str, mark_count: int) -> float:
        """计算检测置信度"""
        confidence = 0.0
        
        # 基于文本长度
        if len(text) > 10:
            confidence += 0.3
        
        # 基于标记类型
        if mark_type != 'unknown':
            confidence += 0.4
        
        # 基于标记数量
        if mark_count > 0:
            confidence += 0.3
        
        # 基于关键词匹配度
        keyword_matches = 0
        for keywords in self.mark_keywords.values():
            for keyword in keywords:
                if keyword.lower() in text.lower():
                    keyword_matches += 1
        
        if keyword_matches > 0:
            confidence += min(0.2 * keyword_matches, 0.5)
        
        return min(confidence, 1.0)
    
    def _create_empty_result(self, phone_number: str) -> Dict[str, any]:
        """创建空的检测结果"""
        return {
            'phone_number': phone_number,
            'has_mark': False,
            'mark_type': 'unknown',
            'mark_count': 0,
            'description': '',
            'confidence': 0.0,
            'timestamp': time.time()
        }
    
    def get_mark_info_quick(self, device, phone_number: str) -> Dict[str, any]:
        """
        快速获取号码标记信息（主要方法）

        Args:
            device: uiautomator2设备对象
            phone_number: 电话号码

        Returns:
            Dict: 标记信息
        """
        return self.detect_marks_from_dialer_ui(device, phone_number)
