"""
号码文件读取模块
支持读取CSV、Excel、TXT等格式的号码文件
"""

import pandas as pd
import csv
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger


class PhoneNumberReader:
    """电话号码文件读取器"""
    
    def __init__(self):
        self.supported_formats = ['.csv', '.xlsx', '.xls', '.txt']
    
    def read_phone_numbers(self, file_path: str, 
                          phone_column: str = None,
                          name_column: str = None) -> List[Dict[str, Any]]:
        """
        读取电话号码文件
        
        Args:
            file_path: 文件路径
            phone_column: 电话号码列名（如果为None则自动检测）
            name_column: 姓名列名（可选）
            
        Returns:
            List[Dict]: 包含电话号码和相关信息的字典列表
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        if file_path.suffix.lower() not in self.supported_formats:
            raise ValueError(f"不支持的文件格式: {file_path.suffix}")
        
        try:
            if file_path.suffix.lower() == '.csv':
                return self._read_csv(file_path, phone_column, name_column)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                return self._read_excel(file_path, phone_column, name_column)
            elif file_path.suffix.lower() == '.txt':
                return self._read_txt(file_path)
        except Exception as e:
            logger.error(f"读取文件失败 {file_path}: {e}")
            raise
    
    def _read_csv(self, file_path: Path, 
                  phone_column: str = None,
                  name_column: str = None) -> List[Dict[str, Any]]:
        """读取CSV文件"""
        try:
            # 尝试不同的编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
            df = None
            
            for encoding in encodings:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    logger.info(f"使用编码 {encoding} 成功读取CSV文件")
                    break
                except UnicodeDecodeError:
                    continue
            
            if df is None:
                raise ValueError("无法使用支持的编码读取CSV文件")
            
            return self._process_dataframe(df, phone_column, name_column)
            
        except Exception as e:
            logger.error(f"读取CSV文件失败: {e}")
            raise
    
    def _read_excel(self, file_path: Path,
                   phone_column: str = None,
                   name_column: str = None) -> List[Dict[str, Any]]:
        """读取Excel文件"""
        try:
            df = pd.read_excel(file_path)
            logger.info(f"成功读取Excel文件: {file_path}")
            return self._process_dataframe(df, phone_column, name_column)
            
        except Exception as e:
            logger.error(f"读取Excel文件失败: {e}")
            raise
    
    def _read_txt(self, file_path: Path) -> List[Dict[str, Any]]:
        """读取TXT文件（每行一个号码）"""
        try:
            phone_numbers = []
            
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line and self._is_valid_phone_number(line):
                        phone_numbers.append({
                            'phone': line,
                            'name': f'号码{line_num}',
                            'source_line': line_num
                        })
            
            logger.info(f"从TXT文件读取到 {len(phone_numbers)} 个号码")
            return phone_numbers
            
        except Exception as e:
            logger.error(f"读取TXT文件失败: {e}")
            raise
    
    def _process_dataframe(self, df: pd.DataFrame,
                          phone_column: str = None,
                          name_column: str = None) -> List[Dict[str, Any]]:
        """处理DataFrame数据"""
        # 自动检测电话号码列
        if phone_column is None:
            phone_column = self._detect_phone_column(df)
        
        if phone_column not in df.columns:
            raise ValueError(f"未找到指定的电话号码列: {phone_column}")
        
        # 自动检测姓名列
        if name_column is None:
            name_column = self._detect_name_column(df)
        
        phone_numbers = []
        
        for index, row in df.iterrows():
            phone = str(row[phone_column]).strip()
            
            if self._is_valid_phone_number(phone):
                record = {
                    'phone': phone,
                    'name': str(row[name_column]).strip() if name_column and name_column in df.columns else f'联系人{index+1}',
                    'source_row': index + 1
                }
                
                # 添加其他列的信息
                for col in df.columns:
                    if col not in [phone_column, name_column]:
                        record[col] = str(row[col]).strip()
                
                phone_numbers.append(record)
        
        logger.info(f"处理完成，有效号码数量: {len(phone_numbers)}")
        return phone_numbers
    
    def _detect_phone_column(self, df: pd.DataFrame) -> str:
        """自动检测电话号码列"""
        possible_names = [
            'phone', 'telephone', 'mobile', 'cell', 'number',
            '电话', '手机', '号码', '联系电话', '手机号', '电话号码'
        ]
        
        for col in df.columns:
            col_lower = str(col).lower()
            if any(name in col_lower for name in possible_names):
                logger.info(f"检测到电话号码列: {col}")
                return col
        
        # 如果没有找到明确的列名，检查数据内容
        for col in df.columns:
            sample_data = df[col].dropna().astype(str).head(10)
            phone_count = sum(1 for x in sample_data if self._is_valid_phone_number(x))
            
            if phone_count >= len(sample_data) * 0.8:  # 80%以上是有效号码
                logger.info(f"根据内容检测到电话号码列: {col}")
                return col
        
        # 默认使用第一列
        first_col = df.columns[0]
        logger.warning(f"无法自动检测电话号码列，使用第一列: {first_col}")
        return first_col
    
    def _detect_name_column(self, df: pd.DataFrame) -> Optional[str]:
        """自动检测姓名列"""
        possible_names = [
            'name', 'contact', 'person', 'user',
            '姓名', '联系人', '名字', '用户', '客户'
        ]
        
        for col in df.columns:
            col_lower = str(col).lower()
            if any(name in col_lower for name in possible_names):
                logger.info(f"检测到姓名列: {col}")
                return col
        
        return None
    
    def _is_valid_phone_number(self, phone: str) -> bool:
        """验证是否为有效的电话号码"""
        if not phone or phone.lower() in ['nan', 'none', '']:
            return False
        
        # 清理号码格式
        clean_phone = ''.join(c for c in phone if c.isdigit() or c == '+')
        
        # 基本长度检查
        if len(clean_phone) < 7 or len(clean_phone) > 15:
            return False
        
        # 中国手机号码格式检查
        if clean_phone.startswith('+86'):
            clean_phone = clean_phone[3:]
        
        if len(clean_phone) == 11 and clean_phone.startswith('1'):
            return True
        
        # 固定电话格式检查（简化）
        if len(clean_phone) >= 7:
            return True
        
        return False
    
    def validate_file_format(self, file_path: str) -> bool:
        """验证文件格式是否支持"""
        file_path = Path(file_path)
        return file_path.suffix.lower() in self.supported_formats
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """获取文件基本信息"""
        file_path = Path(file_path)
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        info = {
            'file_name': file_path.name,
            'file_size': file_path.stat().st_size,
            'file_format': file_path.suffix.lower(),
            'is_supported': self.validate_file_format(str(file_path))
        }
        
        try:
            # 尝试读取前几行获取更多信息
            if file_path.suffix.lower() == '.csv':
                df = pd.read_csv(file_path, nrows=5)
            elif file_path.suffix.lower() in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path, nrows=5)
            else:
                return info
            
            info.update({
                'columns': list(df.columns),
                'estimated_rows': len(df) if len(df) < 5 else "5+"
            })
            
        except Exception as e:
            logger.warning(f"无法获取文件详细信息: {e}")
        
        return info
