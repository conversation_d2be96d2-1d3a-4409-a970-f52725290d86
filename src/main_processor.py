"""
主处理器模块
整合所有功能模块，实现完整的电话号码标记识别流程
"""

import time
import os
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger
from tqdm import tqdm

from .adb_controller import ADBController
from .phone_reader import PhoneNumberReader
from .mark_detector import MarkDetector
from .data_exporter import DataExporter


class PhoneMarkProcessor:
    """电话号码标记处理器"""
    
    def __init__(self,
                 device_id: Optional[str] = None,
                 output_dir: str = "output",
                 screenshots_dir: str = "screenshots"):
        """
        初始化处理器

        Args:
            device_id: Android设备ID
            output_dir: 输出目录
            screenshots_dir: 截图目录
        """
        self.adb_controller = ADBController(device_id)
        self.phone_reader = PhoneNumberReader()
        self.mark_detector = MarkDetector()
        self.data_exporter = DataExporter(output_dir)
        
        # 创建截图目录
        self.screenshots_dir = Path(screenshots_dir)
        self.screenshots_dir.mkdir(exist_ok=True)
        
        # 处理配置
        self.config = {
            'call_timeout': 10,  # 拨号超时时间（秒）
            'detection_delay': 2,  # 检测延迟时间（秒）
            'screenshot_delay': 1,  # 截图延迟时间（秒）
            'between_calls_delay': 3,  # 通话间隔时间（秒）
            'max_retries': 2,  # 最大重试次数
        }
    
    def process_phone_list(self, 
                          file_path: str,
                          phone_column: Optional[str] = None,
                          name_column: Optional[str] = None,
                          start_index: int = 0,
                          max_count: Optional[int] = None) -> Dict[str, Any]:
        """
        处理电话号码列表
        
        Args:
            file_path: 号码文件路径
            phone_column: 电话号码列名
            name_column: 姓名列名
            start_index: 开始索引
            max_count: 最大处理数量
            
        Returns:
            Dict: 处理结果
        """
        logger.info(f"开始处理电话号码列表: {file_path}")
        
        try:
            # 读取电话号码
            phone_numbers = self.phone_reader.read_phone_numbers(
                file_path, phone_column, name_column
            )
            
            if not phone_numbers:
                raise ValueError("未找到有效的电话号码")
            
            # 应用索引和数量限制
            if start_index > 0:
                phone_numbers = phone_numbers[start_index:]
            
            if max_count:
                phone_numbers = phone_numbers[:max_count]
            
            logger.info(f"准备处理 {len(phone_numbers)} 个号码")
            
            # 检查设备连接
            if not self.adb_controller.check_device_connection():
                raise RuntimeError("Android设备未连接")
            
            # 处理每个号码
            results = []
            failed_count = 0
            
            with tqdm(total=len(phone_numbers), desc="处理进度") as pbar:
                for i, phone_info in enumerate(phone_numbers):
                    try:
                        result = self._process_single_phone(phone_info, i)
                        results.append(result)
                        
                        if result.get('call_status') == 'failed':
                            failed_count += 1
                        
                    except Exception as e:
                        logger.error(f"处理号码失败 {phone_info.get('phone', '')}: {e}")
                        failed_count += 1
                        
                        # 创建失败结果
                        failed_result = {
                            'phone_number': phone_info.get('phone', ''),
                            'name': phone_info.get('name', ''),
                            'has_mark': False,
                            'mark_type': 'unknown',
                            'mark_count': 0,
                            'description': f'处理失败: {str(e)}',
                            'confidence': 0.0,
                            'call_status': 'failed',
                            'timestamp': time.time(),
                            'processing_time': 0
                        }
                        results.append(failed_result)
                    
                    finally:
                        pbar.update(1)
                        
                        # 通话间隔
                        if i < len(phone_numbers) - 1:
                            time.sleep(self.config['between_calls_delay'])
            
            # 生成处理报告
            summary = self._generate_processing_summary(results, failed_count)
            
            logger.info(f"处理完成: 成功 {len(results) - failed_count}, 失败 {failed_count}")
            
            return {
                'results': results,
                'summary': summary,
                'total_processed': len(results),
                'success_count': len(results) - failed_count,
                'failed_count': failed_count
            }
            
        except Exception as e:
            logger.error(f"处理电话号码列表失败: {e}")
            raise
    
    def _process_single_phone(self, phone_info: Dict[str, Any], index: int) -> Dict[str, Any]:
        """
        处理单个电话号码
        
        Args:
            phone_info: 电话号码信息
            index: 索引
            
        Returns:
            Dict: 处理结果
        """
        phone_number = phone_info.get('phone', '')
        name = phone_info.get('name', '')
        
        logger.info(f"处理号码: {phone_number} ({name})")
        
        start_time = time.time()
        
        try:
            # 1. 拨打电话
            call_success = self._make_call_with_retry(phone_number)
            
            if not call_success:
                return self._create_failed_result(phone_info, "拨号失败", start_time)
            
            # 2. 等待界面稳定并检测标记
            time.sleep(self.config['detection_delay'])

            # 3. 检测标记信息
            mark_info = self._detect_marks(phone_number)

            # 4. 可选：截图记录（用于调试）
            screenshot_path = self._take_screenshot(phone_number, index)

            # 5. 挂断电话
            self.adb_controller.end_call()
            
            # 6. 完善结果信息
            mark_info.update({
                'name': name,
                'call_status': 'success',
                'processing_time': time.time() - start_time,
                'screenshot_path': str(screenshot_path) if screenshot_path else ''
            })
            
            return mark_info
            
        except Exception as e:
            # 确保挂断电话
            try:
                self.adb_controller.end_call()
            except:
                pass
            
            logger.error(f"处理号码失败 {phone_number}: {e}")
            return self._create_failed_result(phone_info, str(e), start_time)
    
    def _make_call_with_retry(self, phone_number: str) -> bool:
        """带重试的拨号"""
        for attempt in range(self.config['max_retries']):
            try:
                success = self.adb_controller.make_call(phone_number)
                if success:
                    return True
                
                logger.warning(f"拨号失败，重试 {attempt + 1}/{self.config['max_retries']}")
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"拨号异常: {e}")
                
        return False
    
    def _take_screenshot(self, phone_number: str, index: int) -> Optional[Path]:
        """截取屏幕"""
        try:
            timestamp = int(time.time())
            filename = f"screenshot_{index:04d}_{phone_number}_{timestamp}.png"
            screenshot_path = self.screenshots_dir / filename
            
            # 等待截图稳定
            time.sleep(self.config['screenshot_delay'])
            
            success = self.adb_controller.capture_screen(str(screenshot_path))
            
            if success and screenshot_path.exists():
                logger.debug(f"截图成功: {screenshot_path}")
                return screenshot_path
            else:
                logger.warning(f"截图失败: {phone_number}")
                return None
                
        except Exception as e:
            logger.error(f"截图异常 {phone_number}: {e}")
            return None
    
    def _detect_marks(self, phone_number: str) -> Dict[str, Any]:
        """检测标记信息"""
        try:
            # 直接从拨号界面UI获取标记信息
            mark_info = self.mark_detector.get_mark_info_quick(
                self.adb_controller.device, phone_number
            )

            return mark_info

        except Exception as e:
            logger.error(f"标记检测失败 {phone_number}: {e}")
            return self.mark_detector._create_empty_result(phone_number)
    
    def _create_failed_result(self, phone_info: Dict[str, Any], 
                            error_msg: str, start_time: float) -> Dict[str, Any]:
        """创建失败结果"""
        return {
            'phone_number': phone_info.get('phone', ''),
            'name': phone_info.get('name', ''),
            'has_mark': False,
            'mark_type': 'unknown',
            'mark_count': 0,
            'description': f'处理失败: {error_msg}',
            'confidence': 0.0,
            'call_status': 'failed',
            'timestamp': time.time(),
            'processing_time': time.time() - start_time,
            'screenshot_path': ''
        }
    
    def _generate_processing_summary(self, results: List[Dict[str, Any]], 
                                   failed_count: int) -> Dict[str, Any]:
        """生成处理摘要"""
        total_count = len(results)
        success_count = total_count - failed_count
        marked_count = sum(1 for r in results if r.get('has_mark', False))
        
        # 按类型统计
        type_stats = {}
        for result in results:
            mark_type = result.get('mark_type', 'unknown')
            type_stats[mark_type] = type_stats.get(mark_type, 0) + 1
        
        # 计算平均处理时间
        processing_times = [r.get('processing_time', 0) for r in results if r.get('call_status') == 'success']
        avg_processing_time = sum(processing_times) / len(processing_times) if processing_times else 0
        
        return {
            'total_processed': total_count,
            'success_count': success_count,
            'failed_count': failed_count,
            'success_rate': success_count / total_count if total_count > 0 else 0,
            'marked_count': marked_count,
            'mark_rate': marked_count / success_count if success_count > 0 else 0,
            'type_distribution': type_stats,
            'average_processing_time': avg_processing_time,
            'total_processing_time': sum(r.get('processing_time', 0) for r in results)
        }
    
    def export_results(self, results: List[Dict[str, Any]], 
                      formats: List[str] = None) -> Dict[str, str]:
        """
        导出结果
        
        Args:
            results: 处理结果
            formats: 导出格式列表 ['csv', 'excel', 'json']
            
        Returns:
            Dict: 导出文件路径
        """
        if formats is None:
            formats = ['csv', 'excel']
        
        exported_files = {}
        
        try:
            if 'csv' in formats:
                csv_path = self.data_exporter.export_to_csv(results)
                exported_files['csv'] = csv_path
            
            if 'excel' in formats:
                excel_path = self.data_exporter.export_to_excel(results)
                exported_files['excel'] = excel_path
            
            if 'json' in formats:
                json_path = self.data_exporter.export_to_json(results)
                exported_files['json'] = json_path
            
            # 生成图表
            chart_files = self.data_exporter.generate_charts(results)
            if chart_files:
                exported_files['charts'] = chart_files
            
            # 生成汇总报告
            report_path = self.data_exporter.create_summary_report(results)
            exported_files['report'] = report_path
            
            logger.info(f"结果导出完成: {list(exported_files.keys())}")
            
        except Exception as e:
            logger.error(f"导出结果失败: {e}")
            raise
        
        return exported_files
    
    def update_config(self, **kwargs):
        """更新配置"""
        for key, value in kwargs.items():
            if key in self.config:
                self.config[key] = value
                logger.info(f"配置更新: {key} = {value}")
            else:
                logger.warning(f"未知配置项: {key}")
    
    def get_device_info(self) -> Dict[str, Any]:
        """获取设备信息"""
        try:
            devices = self.adb_controller.get_connected_devices()
            current_activity = self.adb_controller.get_current_activity()
            
            return {
                'connected_devices': devices,
                'current_device': self.adb_controller.device_id,
                'device_connected': self.adb_controller.check_device_connection(),
                'current_activity': current_activity
            }
        except Exception as e:
            logger.error(f"获取设备信息失败: {e}")
            return {}
