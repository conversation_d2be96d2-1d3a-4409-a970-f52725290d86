# 电话号码标记识别系统 - 使用指南

## 快速开始

### 1. 环境准备

确保您已经完成以下准备工作：

- ✅ Python 3.9+ 已安装
- ✅ ADB (Android Debug Bridge) 已安装
- ✅ Android设备已连接并启用USB调试
- ✅ 项目依赖已安装 (`pip install -r requirements.txt`)

### 2. 设备连接检查

```bash
# 检查ADB连接
adb devices

# 或使用项目工具检查
python main.py devices
```

### 3. 准备号码文件

支持的格式：
- **CSV文件**: 包含电话号码列的CSV文件
- **Excel文件**: .xlsx 或 .xls 格式
- **TXT文件**: 每行一个电话号码

示例CSV格式：
```csv
电话,姓名,备注
13800138000,张三,朋友
13900139000,李四,同事
************,某公司客服,客服热线
```

### 4. 基本使用

#### 查看文件信息
```bash
python main.py info example_phones.csv
```

#### 试运行（不实际拨号）
```bash
python main.py --file example_phones.csv --dry-run --count 3
```

#### 正式运行
```bash
python main.py --file example_phones.csv --count 5
```

## 详细使用说明

### 命令行参数

| 参数 | 说明 | 示例 |
|------|------|------|
| `--file` | 号码文件路径 | `--file phones.csv` |
| `--phone-column` | 电话号码列名 | `--phone-column "手机号"` |
| `--name-column` | 姓名列名 | `--name-column "联系人"` |
| `--output` | 输出目录 | `--output results` |
| `--start` | 开始索引 | `--start 10` |
| `--count` | 处理数量 | `--count 50` |
| `--format` | 导出格式 | `--format csv excel` |
| `--call-timeout` | 拨号超时 | `--call-timeout 15` |
| `--detection-delay` | 检测延迟 | `--detection-delay 3` |
| `--between-calls-delay` | 通话间隔 | `--between-calls-delay 5` |
| `--verbose` | 详细输出 | `--verbose` |
| `--dry-run` | 试运行模式 | `--dry-run` |

### 使用场景示例

#### 场景1：小批量测试
```bash
# 处理前5个号码，详细输出
python main.py --file phones.csv --count 5 --verbose
```

#### 场景2：大批量处理
```bash
# 从第100个开始处理200个号码，增加延迟避免限制
python main.py --file phones.csv --start 100 --count 200 \
  --call-timeout 15 --detection-delay 3 --between-calls-delay 5
```

#### 场景3：指定列名
```bash
# 指定电话和姓名列
python main.py --file contacts.xlsx \
  --phone-column "手机号码" --name-column "联系人姓名"
```

#### 场景4：多格式导出
```bash
# 导出CSV、Excel和JSON格式
python main.py --file phones.csv --format csv excel json
```

## 输出结果说明

### 文件输出

程序会在输出目录生成以下文件：

1. **主要结果文件**
   - `phone_marks_YYYYMMDD_HHMMSS.csv` - CSV格式结果
   - `phone_marks_YYYYMMDD_HHMMSS.xlsx` - Excel格式结果（包含统计）

2. **统计分析**
   - `summary_report_YYYYMMDD_HHMMSS.txt` - 文本汇总报告
   - `charts_YYYYMMDD_HHMMSS_*.png` - 统计图表

3. **截图记录**
   - `screenshots/` - 处理过程中的屏幕截图

### 结果字段说明

| 字段 | 说明 |
|------|------|
| 电话号码 | 检测的电话号码 |
| 姓名 | 联系人姓名 |
| 是否有标记 | 是否检测到标记信息 |
| 标记类型 | 骚扰电话/快递外卖/商业客服/正常号码/未知 |
| 标记人数 | 标记该号码的用户数量 |
| 描述信息 | 详细的标记描述 |
| 置信度 | 检测结果的可信度 |
| 检测时间 | 处理时间戳 |
| 拨号状态 | 拨号是否成功 |
| 处理时长 | 单个号码处理耗时 |

## 最佳实践

### 1. 处理策略

- **小批量测试**: 先用少量号码测试系统稳定性
- **分批处理**: 大量号码分批处理，避免长时间运行
- **错峰使用**: 避开网络高峰期，提高识别准确性

### 2. 参数调优

- **拨号超时**: 网络较慢时增加到15-20秒
- **检测延迟**: 设备较慢时增加到3-5秒
- **通话间隔**: 避免被限制，建议3-5秒

### 3. 数据管理

- **备份原始数据**: 处理前备份原始号码文件
- **定期清理**: 定期清理截图和临时文件
- **结果验证**: 重要结果建议人工复核

## 故障排除

### 常见问题

#### 1. 设备连接问题
```bash
# 检查设备连接
adb devices

# 重启ADB服务
adb kill-server && adb start-server

# 检查USB调试权限
```

#### 2. 拨号失败
- 检查SIM卡状态
- 确认拨号权限
- 检查网络连接
- 尝试手动拨号测试

#### 3. 识别准确性低
- 增加检测延迟时间
- 检查屏幕亮度和清晰度
- 确认手机界面语言设置

#### 4. 处理速度慢
- 减少检测延迟（在准确性允许的情况下）
- 使用更快的设备
- 优化网络连接

### 日志查看

```bash
# 查看详细日志
tail -f logs/phone_mark_detector.log

# 查看错误日志
grep ERROR logs/phone_mark_detector.log
```

## 安全注意事项

### 1. 合规使用
- 仅用于合法的号码验证
- 遵守当地法律法规
- 不得用于骚扰他人

### 2. 隐私保护
- 妥善保管号码数据
- 及时删除敏感信息
- 不要泄露他人隐私

### 3. 费用控制
- 注意通话费用
- 建议使用无限通话套餐
- 监控话费使用情况

## 技术支持

如遇到问题，请：

1. 查看日志文件获取详细错误信息
2. 运行测试脚本检查系统状态：`python test_system.py`
3. 查阅README.md获取更多技术细节
4. 通过Issue反馈问题

## 更新日志

- v1.0.0: 初始版本发布
  - 支持CSV/Excel/TXT格式号码文件
  - 实现ADB自动拨号和标记识别
  - 提供多格式结果导出
  - 包含统计分析和图表生成
