# 电话号码标记识别系统 - 快速开始

## 🎯 系统简介

这是一个基于ADB的电话号码标记识别工具，能够：
- 自动读取号码表格文件
- 自动拨打电话号码
- 从拨号界面获取号码标记信息
- 立即挂断电话
- 导出详细的分析报告

## 📋 准备工作

### 1. 环境要求
- Python 3.9+
- Android手机（支持ADB调试）
- USB数据线

### 2. 安装步骤

```bash
# 1. 安装Python依赖
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 2. 检查ADB连接
adb devices

# 3. 测试系统
python demo.py
```

### 3. 手机设置

1. **启用开发者选项**：
   - 设置 → 关于手机 → 连续点击"版本号"7次

2. **启用USB调试**：
   - 设置 → 开发者选项 → USB调试（开启）

3. **连接电脑**：
   - 用USB线连接手机和电脑
   - 手机上选择"允许USB调试"

## 🚀 快速使用

### 1. 准备号码文件

创建CSV文件，例如 `my_phones.csv`：
```csv
电话,姓名
13800138000,张三
13900139000,李四
************,某公司客服
```

### 2. 试运行测试

```bash
# 试运行模式（不实际拨号）
python main.py --file my_phones.csv --dry-run --count 3
```

### 3. 正式运行

```bash
# 处理前5个号码
python main.py --file my_phones.csv --count 5 --verbose
```

### 4. 查看结果

程序会在 `output/` 目录生成：
- `phone_marks_YYYYMMDD_HHMMSS.csv` - 检测结果
- `phone_marks_YYYYMMDD_HHMMSS.xlsx` - Excel报告
- `summary_report_YYYYMMDD_HHMMSS.txt` - 汇总报告

## 📊 结果说明

### 标记类型
- **骚扰电话**: 推销、广告、诈骗等
- **快递外卖**: 快递员、外卖配送等  
- **商业客服**: 企业客服、服务热线等
- **正常号码**: 正常联系人
- **未知**: 无标记信息

### 输出字段
- 电话号码、姓名
- 是否有标记、标记类型
- 标记人数、描述信息
- 检测时间、处理状态

## ⚙️ 常用参数

```bash
# 指定列名
python main.py --file phones.xlsx --phone-column "手机号" --name-column "联系人"

# 分批处理
python main.py --file phones.csv --start 10 --count 20

# 调整延迟
python main.py --file phones.csv --call-timeout 15 --detection-delay 3

# 多格式导出
python main.py --file phones.csv --format csv excel json
```

## 🔧 故障排除

### 设备连接问题
```bash
# 检查设备
python main.py devices

# 重启ADB
adb kill-server && adb start-server
```

### 拨号失败
- 检查SIM卡状态
- 确认拨号权限
- 尝试手动拨号测试

### 识别不准确
- 增加检测延迟：`--detection-delay 5`
- 检查手机界面语言设置
- 确保屏幕亮度适中

## ⚠️ 注意事项

### 合规使用
- 仅用于合法的号码验证
- 遵守当地法律法规
- 不得用于骚扰他人

### 费用控制
- 建议使用无限通话套餐
- 监控话费使用情况
- 可设置处理数量限制

### 隐私保护
- 妥善保管号码数据
- 及时删除敏感信息
- 不要泄露他人隐私

## 📖 更多帮助

- **详细文档**: README.md
- **使用指南**: USAGE_GUIDE.md
- **项目总结**: PROJECT_SUMMARY.md
- **功能演示**: `python demo.py`
- **系统测试**: `python test_system.py`

## 🆘 常见问题

**Q: 为什么检测不到设备？**
A: 确保USB调试已启用，并授权电脑调试权限。

**Q: 拨号后没有标记信息？**
A: 可能该号码确实没有标记，或者需要调整检测延迟时间。

**Q: 处理速度太慢？**
A: 可以适当减少延迟时间，但要确保识别准确性。

**Q: 如何处理大量号码？**
A: 建议分批处理，每批50-100个号码，避免长时间运行。

---

🎉 **开始使用吧！** 如有问题，请查看详细文档或运行演示程序。
