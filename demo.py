#!/usr/bin/env python3
"""
电话号码标记识别系统 - 演示脚本
展示核心功能的简化版本
"""

import sys
import time
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def demo_phone_reader():
    """演示号码文件读取功能"""
    print("=" * 60)
    print("📱 演示：号码文件读取功能")
    print("=" * 60)
    
    try:
        from src.phone_reader import PhoneNumberReader
        reader = PhoneNumberReader()
        
        # 读取示例文件
        phone_numbers = reader.read_phone_numbers("example_phones.csv")
        
        print(f"✅ 成功读取 {len(phone_numbers)} 个号码:")
        for i, phone_info in enumerate(phone_numbers[:5], 1):
            print(f"  {i}. {phone_info['phone']} - {phone_info['name']}")
        
        if len(phone_numbers) > 5:
            print(f"  ... 还有 {len(phone_numbers) - 5} 个号码")
            
        return phone_numbers
        
    except Exception as e:
        print(f"❌ 读取失败: {e}")
        return []


def demo_adb_connection():
    """演示ADB连接功能"""
    print("\n" + "=" * 60)
    print("🔗 演示：ADB设备连接")
    print("=" * 60)
    
    try:
        from src.adb_controller import ADBController
        controller = ADBController()
        
        if controller.check_device_connection():
            print("✅ Android设备连接成功")
            
            devices = controller.get_connected_devices()
            print(f"✅ 连接的设备: {devices}")
            
            activity = controller.get_current_activity()
            print(f"✅ 当前Activity: {activity}")
            
            return controller
        else:
            print("❌ 未检测到Android设备")
            print("请确保:")
            print("  1. 手机已连接到电脑")
            print("  2. 已启用USB调试")
            print("  3. 已授权电脑调试权限")
            return None
            
    except Exception as e:
        print(f"❌ 连接失败: {e}")
        return None


def demo_mark_detection():
    """演示标记检测功能"""
    print("\n" + "=" * 60)
    print("🏷️  演示：号码标记识别")
    print("=" * 60)
    
    try:
        from src.mark_detector import MarkDetector
        detector = MarkDetector()
        
        # 模拟不同类型的标记文本
        test_cases = [
            {
                'texts': ['13人标记为骚扰电话', '推销'],
                'phone': '***********',
                'expected': 'spam'
            },
            {
                'texts': ['快递员', '5人标记'],
                'phone': '***********', 
                'expected': 'delivery'
            },
            {
                'texts': ['客服热线', '官方认证'],
                'phone': '************',
                'expected': 'business'
            },
            {
                'texts': ['未知号码'],
                'phone': '***********',
                'expected': 'unknown'
            }
        ]
        
        print("测试不同类型的标记识别:")
        for case in test_cases:
            result = detector._analyze_mark_texts(case['texts'], case['phone'])
            status = "✅" if result['mark_type'] == case['expected'] else "⚠️"
            print(f"  {status} {case['phone']}: {case['texts']} -> {result['mark_type']} ({result['mark_count']}人)")
        
        return detector
        
    except Exception as e:
        print(f"❌ 标记检测演示失败: {e}")
        return None


def demo_data_export():
    """演示数据导出功能"""
    print("\n" + "=" * 60)
    print("📊 演示：数据导出和统计")
    print("=" * 60)
    
    try:
        from src.data_exporter import DataExporter
        exporter = DataExporter("demo_output")
        
        # 创建演示数据
        demo_results = [
            {
                'phone_number': '***********',
                'name': '张三',
                'has_mark': True,
                'mark_type': 'spam',
                'mark_count': 13,
                'description': '13人标记为骚扰电话',
                'confidence': 0.9,
                'timestamp': time.time(),
                'call_status': 'success',
                'processing_time': 3.2
            },
            {
                'phone_number': '***********',
                'name': '李四',
                'has_mark': True,
                'mark_type': 'delivery',
                'mark_count': 5,
                'description': '快递员 5人标记',
                'confidence': 0.8,
                'timestamp': time.time(),
                'call_status': 'success',
                'processing_time': 2.8
            },
            {
                'phone_number': '************',
                'name': '某公司客服',
                'has_mark': True,
                'mark_type': 'business',
                'mark_count': 0,
                'description': '客服热线',
                'confidence': 0.7,
                'timestamp': time.time(),
                'call_status': 'success',
                'processing_time': 2.5
            },
            {
                'phone_number': '***********',
                'name': '王五',
                'has_mark': False,
                'mark_type': 'unknown',
                'mark_count': 0,
                'description': '',
                'confidence': 0.0,
                'timestamp': time.time(),
                'call_status': 'success',
                'processing_time': 2.1
            }
        ]
        
        # 导出CSV
        csv_path = exporter.export_to_csv(demo_results, "demo_results.csv")
        print(f"✅ CSV文件已导出: {csv_path}")
        
        # 生成统计
        stats = exporter._generate_statistics(demo_results)
        print(f"✅ 统计数据生成: {len(stats)} 项统计信息")
        
        # 显示统计摘要
        total = len(demo_results)
        marked = sum(1 for r in demo_results if r['has_mark'])
        print(f"✅ 统计摘要:")
        print(f"  - 总号码数: {total}")
        print(f"  - 有标记数: {marked}")
        print(f"  - 标记率: {marked/total:.1%}")
        
        return exporter
        
    except Exception as e:
        print(f"❌ 数据导出演示失败: {e}")
        return None


def demo_complete_workflow():
    """演示完整工作流程"""
    print("\n" + "=" * 60)
    print("🔄 演示：完整工作流程")
    print("=" * 60)
    
    print("这是一个完整的号码标记识别流程演示:")
    print("1. 读取号码文件")
    print("2. 连接Android设备")
    print("3. 逐个拨打号码")
    print("4. 识别标记信息")
    print("5. 立即挂断")
    print("6. 导出结果")
    
    print("\n⚠️  注意事项:")
    print("- 确保手机已连接并启用USB调试")
    print("- 建议使用无限通话套餐")
    print("- 首次使用建议先试运行")
    
    print("\n🚀 开始使用:")
    print("python main.py --file example_phones.csv --dry-run --count 3")
    print("python main.py --file example_phones.csv --count 5 --verbose")


def main():
    """运行演示"""
    print("🎯 电话号码标记识别系统 - 功能演示")
    print("=" * 60)
    print("本演示将展示系统的核心功能模块")
    
    # 演示各个功能模块
    phone_numbers = demo_phone_reader()
    controller = demo_adb_connection()
    detector = demo_mark_detection()
    exporter = demo_data_export()
    
    # 演示完整流程
    demo_complete_workflow()
    
    print("\n" + "=" * 60)
    print("🎉 演示完成!")
    print("=" * 60)
    
    if controller:
        print("✅ 系统已准备就绪，可以开始使用")
        print("📖 详细使用方法请参考 USAGE_GUIDE.md")
    else:
        print("⚠️  请先连接Android设备后再使用")
        print("📱 设备连接指南请参考 README.md")


if __name__ == "__main__":
    main()
