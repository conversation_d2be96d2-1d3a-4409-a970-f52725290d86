# 电话号码标记识别系统

基于ADB的电话号码标记识别工具，通过自动拨号识别电话号码的标记信息（如骚扰电话、快递外卖等），并生成详细的分析报告。

## 功能特点

- ✅ **多格式支持**: 支持CSV、Excel、TXT格式的号码文件
- ✅ **自动拨号**: 通过ADB控制Android设备自动拨号
- ✅ **智能识别**: 使用OCR和UI自动化技术识别标记信息
- ✅ **快速挂断**: 检测到标记后立即挂断，最小化打扰
- ✅ **批量处理**: 支持大批量号码的自动化处理
- ✅ **多格式导出**: 支持CSV、Excel、JSON格式导出
- ✅ **统计分析**: 自动生成统计图表和分析报告
- ✅ **进度跟踪**: 实时显示处理进度和状态

## 系统要求

### 硬件要求
- 电脑（Windows/macOS/Linux）
- Android手机（支持ADB调试）
- USB数据线

### 软件要求
- Python 3.9+
- Android Debug Bridge (ADB)
- Tesseract OCR（可选，用于文字识别）

## 安装步骤

### 1. 克隆项目
```bash
git clone <repository-url>
cd phone-mark-detector
```

### 2. 安装Python依赖
```bash
pip install -r requirements.txt
```

### 3. 安装ADB
- **Windows**: 下载Android SDK Platform Tools
- **macOS**: `brew install android-platform-tools`
- **Linux**: `sudo apt install android-tools-adb`

### 4. 安装Tesseract（可选）
- **Windows**: 下载安装包 https://github.com/tesseract-ocr/tesseract
- **macOS**: `brew install tesseract`
- **Linux**: `sudo apt install tesseract-ocr tesseract-ocr-chi-sim`

## 使用方法

### 1. 准备工作

#### 启用Android设备的USB调试
1. 进入设置 → 关于手机
2. 连续点击"版本号"7次启用开发者选项
3. 进入设置 → 开发者选项
4. 启用"USB调试"

#### 连接设备
```bash
# 检查设备连接
adb devices

# 或使用工具检查
python main.py devices
```

### 2. 准备号码文件

支持以下格式：

#### CSV格式示例
```csv
电话,姓名
13800138000,张三
13900139000,李四
```

#### Excel格式
包含电话号码和姓名列的Excel文件

#### TXT格式
每行一个电话号码

### 3. 运行检测

#### 基本用法
```bash
python main.py --file phone_list.csv
```

#### 指定列名
```bash
python main.py --file phone_list.xlsx --phone-column "电话" --name-column "姓名"
```

#### 高级选项
```bash
python main.py \
  --file phone_list.csv \
  --output results \
  --start 10 \
  --count 50 \
  --format csv excel json \
  --call-timeout 15 \
  --detection-delay 3 \
  --verbose
```

### 4. 查看结果

程序会在输出目录生成以下文件：
- `phone_marks_YYYYMMDD_HHMMSS.csv` - CSV格式结果
- `phone_marks_YYYYMMDD_HHMMSS.xlsx` - Excel格式结果（包含统计分析）
- `summary_report_YYYYMMDD_HHMMSS.txt` - 文本格式汇总报告
- `charts_YYYYMMDD_HHMMSS_*.png` - 统计图表

## 命令行参数

| 参数 | 简写 | 说明 | 默认值 |
|------|------|------|--------|
| `--file` | `-f` | 输入号码文件路径 | 必需 |
| `--phone-column` | `-p` | 电话号码列名 | 自动检测 |
| `--name-column` | `-n` | 姓名列名 | 自动检测 |
| `--output` | `-o` | 输出目录 | output |
| `--device` | `-d` | Android设备ID | 第一个设备 |
| `--tesseract` | `-t` | Tesseract路径 | 系统路径 |
| `--start` | `-s` | 开始索引 | 0 |
| `--count` | `-c` | 最大处理数量 | 全部 |
| `--format` | | 导出格式 | csv,excel |
| `--call-timeout` | | 拨号超时(秒) | 10 |
| `--detection-delay` | | 检测延迟(秒) | 2 |
| `--between-calls-delay` | | 通话间隔(秒) | 3 |
| `--verbose` | `-v` | 详细输出 | False |
| `--dry-run` | | 试运行模式 | False |

## 工具命令

### 查看连接的设备
```bash
python main.py devices
```

### 查看文件信息
```bash
python main.py info phone_list.csv
```

## 输出格式

### CSV/Excel结果字段
- **电话号码**: 检测的电话号码
- **姓名**: 联系人姓名
- **是否有标记**: 是否检测到标记
- **标记类型**: 标记类型（骚扰电话、快递外卖等）
- **标记人数**: 标记的人数
- **描述信息**: 详细描述
- **置信度**: 检测置信度
- **检测时间**: 检测时间戳
- **拨号状态**: 拨号是否成功
- **处理时长**: 处理耗时

### 标记类型
- **骚扰电话**: 推销、广告、诈骗等
- **快递外卖**: 快递员、外卖配送等
- **商业客服**: 企业客服、服务热线等
- **正常号码**: 正常联系人
- **未知**: 无标记或无法识别

## 注意事项

### 使用须知
1. **合法使用**: 仅用于合法的号码验证，不得用于骚扰他人
2. **隐私保护**: 注意保护个人隐私和号码信息安全
3. **网络费用**: 拨号可能产生通话费用，建议使用无限通话套餐
4. **设备要求**: 确保Android设备电量充足，网络连接稳定

### 技术限制
1. **识别准确性**: OCR识别可能存在误差，建议人工复核重要结果
2. **设备兼容性**: 不同Android版本和厂商定制可能影响兼容性
3. **网络依赖**: 需要稳定的网络连接获取标记信息
4. **处理速度**: 为避免被限制，程序会控制拨号频率

## 故障排除

### 常见问题

#### 1. 设备连接失败
```bash
# 检查ADB连接
adb devices

# 重启ADB服务
adb kill-server
adb start-server
```

#### 2. 拨号失败
- 检查手机是否有拨号权限
- 确认SIM卡状态正常
- 检查网络连接

#### 3. OCR识别失败
- 安装Tesseract OCR
- 检查中文语言包
- 调整检测延迟时间

#### 4. 权限问题
- 确保USB调试已启用
- 允许计算机调试权限
- 检查应用权限设置

### 日志查看
程序运行日志保存在 `logs/phone_mark_detector.log`

## 开发说明

### 项目结构
```
phone-mark-detector/
├── src/
│   ├── __init__.py
│   ├── adb_controller.py      # ADB控制模块
│   ├── phone_reader.py        # 号码文件读取
│   ├── mark_detector.py       # 标记识别模块
│   ├── data_exporter.py       # 数据导出模块
│   └── main_processor.py      # 主处理器
├── main.py                    # 命令行入口
├── requirements.txt           # Python依赖
└── README.md                  # 说明文档
```

### 扩展开发
- 支持更多标记类型识别
- 添加Web界面
- 集成更多数据源
- 优化识别算法

## 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过Issue联系。
