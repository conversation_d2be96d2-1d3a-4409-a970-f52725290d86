#!/usr/bin/env python3
"""
系统测试脚本
用于测试电话号码标记识别系统的各个模块
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
sys.path.insert(0, str(Path(__file__).parent / "src"))

def test_phone_reader():
    """测试号码文件读取模块"""
    print("=" * 50)
    print("测试号码文件读取模块")
    print("=" * 50)
    
    try:
        from src.phone_reader import PhoneNumberReader
        reader = PhoneNumberReader()
        
        # 测试CSV文件读取
        phone_numbers = reader.read_phone_numbers("example_phones.csv")
        print(f"✅ 成功读取 {len(phone_numbers)} 个号码")
        
        for i, phone_info in enumerate(phone_numbers[:3]):
            print(f"  {i+1}. {phone_info.get('phone', '')} - {phone_info.get('name', '')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 号码文件读取测试失败: {e}")
        return False


def test_adb_connection():
    """测试ADB连接"""
    print("\n" + "=" * 50)
    print("测试ADB连接")
    print("=" * 50)
    
    try:
        from src.adb_controller import ADBController
        controller = ADBController()
        
        # 检查设备连接
        if controller.check_device_connection():
            print("✅ ADB设备连接正常")
            
            # 获取设备列表
            devices = controller.get_connected_devices()
            print(f"✅ 连接的设备: {devices}")
            
            # 获取当前Activity
            activity = controller.get_current_activity()
            print(f"✅ 当前Activity: {activity}")
            
            return True
        else:
            print("❌ ADB设备未连接")
            return False
            
    except Exception as e:
        print(f"❌ ADB连接测试失败: {e}")
        return False


def test_mark_detector():
    """测试标记检测模块"""
    print("\n" + "=" * 50)
    print("测试标记检测模块")
    print("=" * 50)
    
    try:
        from src.mark_detector import MarkDetector
        detector = MarkDetector()

        # 测试文本分析
        test_texts = [
            ["13人标记为骚扰电话"],
            ["快递员", "5人标记"],
            ["客服热线"],
            ["未知号码"]
        ]

        for texts in test_texts:
            result = detector._analyze_mark_texts(texts, "13800138000")
            print(f"✅ 文本: {texts} -> 类型: {result['mark_type']}, 数量: {result['mark_count']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 标记检测测试失败: {e}")
        return False


def test_data_exporter():
    """测试数据导出模块"""
    print("\n" + "=" * 50)
    print("测试数据导出模块")
    print("=" * 50)
    
    try:
        from src.data_exporter import DataExporter
        exporter = DataExporter("test_output")
        
        # 创建测试数据
        test_results = [
            {
                'phone_number': '13800138000',
                'name': '张三',
                'has_mark': True,
                'mark_type': 'spam',
                'mark_count': 13,
                'description': '13人标记为骚扰电话',
                'confidence': 0.9,
                'timestamp': 1640995200,
                'call_status': 'success',
                'processing_time': 5.2
            },
            {
                'phone_number': '13900139000',
                'name': '李四',
                'has_mark': False,
                'mark_type': 'unknown',
                'mark_count': 0,
                'description': '',
                'confidence': 0.0,
                'timestamp': 1640995260,
                'call_status': 'success',
                'processing_time': 3.8
            }
        ]
        
        # 测试CSV导出
        csv_path = exporter.export_to_csv(test_results, "test_results.csv")
        print(f"✅ CSV导出成功: {csv_path}")
        
        # 测试统计生成
        stats = exporter._generate_statistics(test_results)
        print(f"✅ 统计数据生成成功: {len(stats)} 行")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据导出测试失败: {e}")
        return False


def test_main_processor():
    """测试主处理器"""
    print("\n" + "=" * 50)
    print("测试主处理器")
    print("=" * 50)
    
    try:
        from src.main_processor import PhoneMarkProcessor
        processor = PhoneMarkProcessor()
        
        # 测试设备信息获取
        device_info = processor.get_device_info()
        print(f"✅ 设备信息获取成功: {device_info.get('device_connected', False)}")
        
        # 测试配置更新
        processor.update_config(call_timeout=15, detection_delay=3)
        print("✅ 配置更新成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 主处理器测试失败: {e}")
        return False


def main():
    """运行所有测试"""
    print("电话号码标记识别系统 - 模块测试")
    print("=" * 60)
    
    tests = [
        ("号码文件读取", test_phone_reader),
        ("ADB连接", test_adb_connection),
        ("标记检测", test_mark_detector),
        ("数据导出", test_data_exporter),
        ("主处理器", test_main_processor),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统准备就绪。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查相关模块。")
        return 1


if __name__ == "__main__":
    sys.exit(main())
